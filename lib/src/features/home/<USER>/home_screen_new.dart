import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/base.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';
import 'package:flutter_kit/src/features/home/<USER>/job_list_item.dart';
import 'package:flutter_kit/src/shared/components/custom_list_view/custom_list_view.dart';
import 'package:flutter_kit/src/shared/extensions/asset_ext.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../logic/home_logic.dart';

@RoutePage()
class HomeScreenNew extends ViewStateWidget<HomeLogic> {
  const HomeScreenNew({super.key});

  @override
  HomeLogic createController() {
    return locator<HomeLogic>();
  }

  @override
  Widget? buildCustomAppBar(BuildContext context, HomeLogic logic) {
    return null;
  }

  @override
  Widget buildBody(BuildContext context, HomeLogic logic) {
    final ValueNotifier<bool> isRefreshing = ValueNotifier<bool>(false);

    // 设置刷新开始回调
    logic.onRefreshStart ??= () {
      isRefreshing.value = true;
    };

    // 设置刷新结束回调
    logic.onRefreshEnd ??= () {
      isRefreshing.value = false;
    };

    // 设置刷新成功回调
    logic.onRefreshSuccess ??= () {
      // 创建一个自定义的顶部提示
      final overlay = Overlay.of(context);
      final overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
          top: MediaQuery.of(context).padding.top + 10, // 状态栏下方10像素
          left: 0,
          right: 0,
          child: Center(
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Color(0xFFF02E4B), // 使用指定的颜色
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min, // 自适应内容宽度
                  children: [
                    Icon(Icons.check_circle, color: Colors.white, size: 16),
                    SizedBox(width: 8),
                    Text(
                      '刷新成功！',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );

      // 显示提示并在2秒后移除
      overlay.insert(overlayEntry);
      Future.delayed(Duration(seconds: 2), () {
        overlayEntry.remove();
      });
    };

    return Stack(
      children: [
        // 背景图片
        Column(
          children: [
            Image.asset(
              'bg_index'.png,
              width: double.infinity,
              height: 240.w,
              fit: BoxFit.cover,
            ),
            Expanded(child: Container(color: AppColors.color_background))
          ],
        ),

        // 页面内容
        Builder(
          builder: (context) {
            final ValueNotifier<double> appBarOpacity =
                ValueNotifier<double>(0.0);

            return RefreshIndicator(
              onRefresh: () async {
                logic.refreshPaging();
              },
              child: NotificationListener<ScrollNotification>(
                onNotification: (ScrollNotification notification) {
                  if (notification is ScrollUpdateNotification) {
                    // 计算滚动距离，最大100像素时完全变白
                    final double offset = notification.metrics.pixels;
                    final double opacity = (offset / 100.0).clamp(0.0, 1.0);
                    appBarOpacity.value = opacity;
                  }
                  return false;
                },
                child: CustomScrollView(
                  slivers: [
                    // 动态背景色的AppBar
                    ValueListenableBuilder<double>(
                      valueListenable: appBarOpacity,
                      builder: (context, opacity, child) {
                        return SliverAppBar(
                          title: _buildTitleBar(),
                          backgroundColor:
                              Colors.white.withValues(alpha: opacity),
                          elevation: 0,
                          pinned: true,
                          floating: false,
                          toolbarHeight: kToolbarHeight,
                        );
                      },
                    ),
                    // Banner 部分
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: EdgeInsets.fromLTRB(12.w, 16.w, 12.w, 16.w),
                        child: _buildBanner(),
                      ),
                    ),
                    // Tabs 部分
                    SliverToBoxAdapter(
                      child: _buildTabs(),
                    ),
                    // Job Expectation 部分
                    SliverToBoxAdapter(
                      child: Container(
                        margin: EdgeInsets.fromLTRB(12.w, 12.w, 12.w, 6.w),
                        child: _buildJobExpection(),
                      ),
                    ),
                    //职位列表部分 - 带上滑
                    CustomListView(
                      dataSource: logic.dataSource,
                      itemBuilder: (context, item, index) {
                        return JobListItem(item: item, index: index);
                      },
                      enableRefresh: true,
                      enableLoadMore: true,
                      enableItemAnimation: true,
                      padding:
                          EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                      separatorBuilder: (context, index) =>
                          SizedBox(height: 12.h),
                      emptyWidget: StateWidgetBuilder.empty(),
                      errorWidget: StateWidgetBuilder.error(),
                    )
                    // 职位列表部分
                    // SliverList(
                    //   delegate: SliverChildBuilderDelegate(
                    //     (context, index) {
                    //       if (index < logic.jobs.length) {
                    //         return JobListItem(
                    //           item: logic.jobs[index],
                    //           index: index,
                    //         );
                    //       } else if (index == logic.jobs.length) {
                    //         // 自动触发加载更多
                    //         WidgetsBinding.instance.addPostFrameCallback((_) {
                    //           print('触发加载更多，当前页：${logic.curPage}，数据量：${logic.jobs.length}');
                    //           logic.loadMorePaging();
                    //         });
                    //
                    //         // 显示加载指示器
                    //         return Container(
                    //           padding: EdgeInsets.all(20.w),
                    //           alignment: Alignment.center,
                    //           child: Row(
                    //             mainAxisAlignment: MainAxisAlignment.center,
                    //             children: [
                    //               SizedBox(
                    //                 width: 16.w,
                    //                 height: 16.w,
                    //                 child: CircularProgressIndicator(
                    //                   strokeWidth: 2,
                    //                   valueColor: AlwaysStoppedAnimation<Color>(Colors.grey.shade600),
                    //                 ),
                    //               ),
                    //               SizedBox(width: 12.w),
                    //               Text(
                    //                 '正在加载更多数据...',
                    //                 style: TextStyle(
                    //                   fontSize: 14.w,
                    //                   color: Colors.grey.shade600,
                    //                 ),
                    //               ),
                    //             ],
                    //           ),
                    //         );
                    //       }
                    //       return null;
                    //     },
                    //     childCount: logic.jobs.length + 1, // +1 for load more indicator
                    //   ),
                    // ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  @override
  bool useScaffold() => true;

  @override
  bool hasData() {
    final logic = createController();
    return logic.jobs.isNotEmpty;
  }

  @override
  Widget buildTopLoadingWidget() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        height: 3,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue, Colors.blue.shade300],
          ),
        ),
        child: LinearProgressIndicator(
          backgroundColor: Colors.transparent,
          valueColor: AlwaysStoppedAnimation<Color>(
              Colors.white.withValues(alpha: 0.8)),
        ),
      ),
    );
  }

  Widget _buildTitleBar() {
    return Container(
      padding: EdgeInsets.fromLTRB(0, 0, 0, 12.w),
      child: Row(
        children: [
          Row(
            children: [
              Icon(Icons.location_on,
                  size: 14.w, color: AppColors.color_333333),
              SizedBox(width: 2.w),
              Text(
                '汕头',
                style: TextStyle(
                  fontSize: 13.w,
                  fontWeight: FontWeight.bold,
                  color: AppColors.color_333333,
                ),
              ),
              SizedBox(width: 0.w),
              Icon(Icons.arrow_drop_down,
                  size: 20.w, color: AppColors.color_333333),
            ],
          ),
          SizedBox(width: 6.w),
          Expanded(
            child: Card(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.w)),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.w),
                child: Row(
                  children: [
                    Icon(Icons.search,
                        size: 20.w, color: AppColors.color_333333),
                    SizedBox(width: 6),
                    Expanded(
                        child: Text(
                      '搜索职位名称',
                      style: TextStyle(
                          fontSize: 15.w, color: AppColors.color_cccccc),
                    )),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabs() {
    final List<Map<String, String>> tabs = [
      {'text': '全职', 'icon': 'ic_ygz'},
      {'text': '兼职', 'icon': 'ic_ygz'},
      {'text': '普工', 'icon': 'ic_ygz'},
      {'text': '销售', 'icon': 'ic_ygz'},
      {'text': '招聘会', 'icon': 'ic_ygz'}
    ];

    return Container(
      margin: EdgeInsets.fromLTRB(12.w, 0, 12.w, 0),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(12.w),
          child: Container(
            height: 80.h,
            color: Colors.white,
            child: Row(
                children: tabs
                    .map((tab) => Expanded(
                            child: Container(
                          margin: EdgeInsets.symmetric(
                              vertical: 8.w, horizontal: 8.w),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                  height: 30.w,
                                  width: 30.w,
                                  child: Image.asset(
                                    'assets/images/${tab['icon']}.png',
                                    fit: BoxFit.cover,
                                  )),
                              SizedBox(height: 6.w),
                              Text(
                                tab['text']!,
                                style: TextStyle(
                                    fontSize: 13.w,
                                    color: AppColors.color_333333,
                                    fontWeight: FontWeight.w600),
                              )
                            ],
                          ),
                        )))
                    .toList()),
          )),
    );
  }

  Widget _buildBanner() {
    return SizedBox(
      height: 160.w,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.w),
        child: PageView(
          children: [
            Image.asset(
              'assets/images/bg_gongsi.jpg',
              fit: BoxFit.cover,
            ),
            Image.asset(
              'assets/images/bg_gongsi.jpg',
              fit: BoxFit.cover,
            )
          ],
        ),
      ),
    );
  }

  Widget _buildJobExpection() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10.w),
      child: Container(
        color: Colors.white,
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 15.w, horizontal: 12.w),
          child: Row(
            children: [
              Text(
                '行政专员',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.w),
              ),
              Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                      child: ClipRRect(
                          borderRadius: BorderRadius.circular(14.w),
                          child: Container(
                            color: AppColors.color_ff0027,
                            child: Row(
                              children: [
                                SizedBox(
                                    width: 22.w,
                                    height: 22.w,
                                    child: Image.asset(
                                      'assets/images/ic_zaizhi_yuenei.png',
                                      fit: BoxFit.cover,
                                    )),
                                SizedBox(width: 8.w),
                                Text(
                                  '暂不考虑',
                                  style: TextStyle(
                                      fontSize: 11.w,
                                      color: AppColors.color_666666),
                                ),
                                SizedBox(width: 8.w)
                              ],
                            ),
                          ))),
                  SizedBox(width: 10.w),
                  SizedBox(
                    width: 5.w,
                    child: Image.asset('assets/images/ic_arrow_see.png',
                        fit: BoxFit.cover),
                  )
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
