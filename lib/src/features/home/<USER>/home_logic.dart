import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/logic/view_state_paging_logic.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/models/job_info_entity.dart';
import '../../../base/base.dart';
import '../../../datasource/repositories/home_repository.dart';
import '../../../shared/components/custom_list_view/custom_list_data_source.dart';
import '../../../shared/components/custom_list_view/custom_list_logic.dart';

class HomeLogic extends ViewStateLogic {
  final HomeReportsitory repository;
  List<JobInfoEntity> _jobs = [];
  VoidCallback? onRefreshSuccess;
  VoidCallback? onRefreshStart;
  VoidCallback? onRefreshEnd;

  List<JobInfoEntity> get jobs => _jobs;

  /// CustomListView的Logic实例
  late CustomListLogic<JobInfoEntity> listLogic;
  /// 数据源
  late CustomListDataSource<JobInfoEntity> dataSource;

  HomeLogic({required this.repository}) {

  }



  @override
  void loadData() {
    listLogic.refresh();
  }

  @override
  void refreshPaging() {
    listLogic.refresh();
    // // 如果已有数据，不改变ViewState，只在后台刷新
    // final bool hasExistingData = _jobs.isNotEmpty;
    //
    // // 如果有数据，触发刷新开始回调（显示顶部loading）
    // if (hasExistingData && onRefreshStart != null) {
    //   onRefreshStart!();
    // }
    //
    // sendRequest<List<JobInfoEntity>>(
    //   _fetchJobs(1),
    //   bindViewState: !hasExistingData, // 有数据时不绑定ViewState
    //   successCallback: (data) {
    //     _jobs = data ?? [];
    //     curPage = 1; // 重置页码
    //     notifyListeners();
    //
    //     // 触发刷新结束回调
    //     if (onRefreshEnd != null) {
    //       onRefreshEnd!();
    //     }
    //
    //     // 如果有数据，显示刷新成功提示
    //     if (_jobs.isNotEmpty && onRefreshSuccess != null) {
    //       onRefreshSuccess!();
    //     }
    //   },
    //   failCallback: () {
    //     // 失败时也要结束loading
    //     if (onRefreshEnd != null) {
    //       onRefreshEnd!();
    //     }
    //   }
    // );
  }



  @override
  void loadMorePaging() {
    listLogic.loadMore();
    // // 如果没有更多数据，不触发加载更多
    // sendRequest<List<JobInfoEntity>>(_fetchJobs(curPage + 1),
    //   bindViewState: false, // 加载更多时不改变主要状态
    //   successCallback: (data) {
    //     if (data != null) {
    //       _jobs.addAll(data);
    //       curPage++;
    //       notifyListeners();
    //     }
    //   }
    // );
  }

  Future<UnifiedResponse<List<JobInfoEntity>>> _fetchJobs(int page) async {
    return await repository.getHomeList(page);
  }

  Future<void> fetchJobs() async {
    loadData();
  }
}
